#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
南海CSV文件URL修复和时间提取脚本 v2
先修复URL格式并保存到CSV，然后进行时间提取
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import re
import time
import random
import logging
from datetime import datetime
from typing import List, Optional, Tuple
import os
import concurrent.futures
from threading import Lock
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nanhai_url_fix.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class URLFixer:
    """URL修复器"""
    
    def fix_url_format(self, url: str) -> str:
        """智能修复URL格式，区分路径分隔符和内容中的连字符"""
        if not url or url == '无法获取':
            return url

        original_url = url

        # 检查是否是被错误替换的URL
        if '-' in url and ('http' in url or 'www.' in url):

            if '://' in url:
                protocol, rest = url.split('://', 1)

                # 智能分析URL结构
                # 常见的URL模式分析
                fixed_url = self._smart_fix_url_path(protocol, rest)

            else:
                # 没有协议的情况，保守处理
                fixed_url = url

            if fixed_url != original_url:
                logger.debug(f"URL修复: {original_url} -> {fixed_url}")
            return fixed_url

        return url

    def _smart_fix_url_path(self, protocol: str, rest: str) -> str:
        """智能修复URL路径部分"""

        # 特殊处理已知的网站模式
        if 'pacom.mil' in rest:
            return self._fix_pacom_url(protocol, rest)
        elif 'army.mil' in rest:
            return self._fix_army_url(protocol, rest)
        elif 'marines.mil' in rest:
            return self._fix_marines_url(protocol, rest)
        elif 'navy.mil' in rest:
            return self._fix_navy_url(protocol, rest)
        elif 'pacaf.af.mil' in rest:
            return self._fix_pacaf_url(protocol, rest)
        elif 'dvidshub.net' in rest:
            return self._fix_dvidshub_url(protocol, rest)
        elif 'usembassy.gov' in rest:
            return self._fix_embassy_url(protocol, rest)
        elif 'facebook.com' in rest:
            return self._fix_facebook_url(protocol, rest)
        else:
            # 通用修复逻辑
            return self._fix_generic_url(protocol, rest)

    def _fix_pacom_url(self, protocol: str, rest: str) -> str:
        """修复pacom.mil的URL"""
        # pacom.mil的URL通常是: https://www.pacom.mil/Media/News/News/Article/View/Article/数字/标题/
        parts = rest.split('-')
        if len(parts) >= 2:
            domain = parts[0]  # www.pacom.mil

            # 查找数字ID（通常在Article后面）
            for i in range(1, len(parts)):
                part = parts[i]
                if part.isdigit() and len(part) >= 6:  # 文章ID通常是6-7位数字
                    # 找到文章ID，构建标准路径
                    article_id = part
                    title_parts = parts[i+1:]  # ID后面的是标题，保持连字符

                    # 构建标准的pacom.mil路径
                    fixed_parts = [domain, 'Media', 'News', 'News', 'Article', 'View', 'Article', article_id]
                    if title_parts:
                        # 标题部分保持连字符连接
                        title = '-'.join([p for p in title_parts if p])
                        if title:
                            fixed_parts.append(title)

                    return f"{protocol}://" + '/'.join(fixed_parts) + ('/' if rest.endswith('-') else '')

            # 如果没找到数字ID，使用通用方法
            return f"{protocol}://{domain}/" + '/'.join(parts[1:])

        return f"{protocol}://{rest}"

    def _fix_army_url(self, protocol: str, rest: str) -> str:
        """修复army.mil的URL"""
        # army.mil的URL通常是: https://www.army.mil/article/数字/标题
        parts = rest.split('-')
        if len(parts) >= 3:
            domain = parts[0]  # www.army.mil
            if parts[1] == 'article' and parts[2].isdigit():
                article_id = parts[2]
                title_parts = parts[3:]
                title = '_'.join([p for p in title_parts if p])  # army.mil用下划线
                return f"{protocol}://{domain}/article/{article_id}/{title}"

        return f"{protocol}://{rest.replace('-', '/')}"

    def _fix_marines_url(self, protocol: str, rest: str) -> str:
        """修复marines.mil的URL"""
        # marines.mil的URL通常是: https://www.marines.mil/News/News/Display/Article/数字/标题/
        return self._fix_military_news_url(protocol, rest)

    def _fix_navy_url(self, protocol: str, rest: str) -> str:
        """修复navy.mil的URL"""
        # navy.mil的URL通常是: https://www.navy.mil/Press-Office/News-Stories/Article/数字/标题/
        return self._fix_military_news_url(protocol, rest)

    def _fix_military_news_url(self, protocol: str, rest: str) -> str:
        """修复军方新闻网站的通用URL格式"""
        parts = rest.split('-')
        if len(parts) >= 2:
            domain = parts[0]

            # 查找数字ID
            for i in range(1, len(parts)):
                part = parts[i]
                if part.isdigit() and len(part) >= 6:
                    # 找到文章ID，构建路径
                    article_id = part
                    title_parts = parts[i+1:]

                    # 根据域名构建标准路径
                    if 'marines.mil' in domain:
                        fixed_parts = [domain, 'News', 'News', 'Display', 'Article', article_id]
                    elif 'navy.mil' in domain:
                        fixed_parts = [domain, 'Press-Office', 'News-Stories', 'Article', article_id]
                    else:
                        # 通用格式
                        path_before_id = parts[1:i]
                        fixed_parts = [domain] + path_before_id + [article_id]

                    if title_parts:
                        title = '-'.join([p for p in title_parts if p])
                        if title:
                            fixed_parts.append(title)

                    return f"{protocol}://" + '/'.join(fixed_parts) + '/'

            # 没找到数字ID，简单替换
            return f"{protocol}://{domain}/" + '/'.join(parts[1:])

        return f"{protocol}://{rest.replace('-', '/')}"

    def _fix_pacaf_url(self, protocol: str, rest: str) -> str:
        """修复pacaf.af.mil的URL"""
        return self._fix_military_news_url(protocol, rest)

    def _fix_dvidshub_url(self, protocol: str, rest: str) -> str:
        """修复dvidshub.net的URL"""
        # dvidshub.net的URL通常是: https://www.dvidshub.net/news/数字/标题
        parts = rest.split('-')
        if len(parts) >= 3:
            domain = parts[0]
            if parts[1] == 'news' and parts[2].isdigit():
                news_id = parts[2]
                title_parts = parts[3:]
                title = '-'.join([p for p in title_parts if p])
                return f"{protocol}://{domain}/news/{news_id}/{title}"

        return f"{protocol}://{rest.replace('-', '/')}"

    def _fix_embassy_url(self, protocol: str, rest: str) -> str:
        """修复usembassy.gov的URL"""
        # 大使馆网站通常用连字符作为URL的一部分
        parts = rest.split('-')
        if len(parts) >= 2:
            domain = parts[0]
            path_parts = parts[1:]
            # 保持连字符，只在明显的路径分隔处使用斜杠
            path = '-'.join(path_parts)
            return f"{protocol}://{domain}/{path}"

        return f"{protocol}://{rest}"

    def _fix_facebook_url(self, protocol: str, rest: str) -> str:
        """修复facebook.com的URL"""
        # Facebook URL通常是: https://www.facebook.com/page/posts/id
        parts = rest.split('-')
        if len(parts) >= 2:
            domain = parts[0]
            path_parts = parts[1:]
            return f"{protocol}://{domain}/" + '/'.join(path_parts)

        return f"{protocol}://{rest}"

    def _fix_generic_url(self, protocol: str, rest: str) -> str:
        """通用URL修复逻辑"""
        parts = rest.split('-')
        if len(parts) >= 2:
            domain = parts[0]
            path_parts = parts[1:]

            # 查找数字ID，通常是文章标识符
            fixed_parts = [domain]
            for i, part in enumerate(path_parts):
                if part.isdigit() and len(part) >= 4:
                    # 找到可能的ID，前面的作为路径，后面的作为标题
                    path_before_id = path_parts[:i]
                    article_id = part
                    title_parts = path_parts[i+1:]

                    fixed_parts.extend(path_before_id)
                    fixed_parts.append(article_id)
                    if title_parts:
                        # 标题部分保持连字符
                        title = '-'.join([p for p in title_parts if p])
                        if title:
                            fixed_parts.append(title)
                    break
            else:
                # 没找到数字ID，简单替换
                fixed_parts.extend(path_parts)

            return f"{protocol}://" + '/'.join(fixed_parts)

        return f"{protocol}://{rest}"

def fix_urls_in_csv():
    """修复CSV文件中的URL格式"""
    csv_path = 'extracted_data/南海_extracted.csv'
    output_path = 'extracted_data/南海_extracted_fixed_urls.csv'
    
    # 备份原文件
    if os.path.exists(csv_path):
        import shutil
        backup_path = 'extracted_data/南海_extracted_backup_v2.csv'
        shutil.copy2(csv_path, backup_path)
        logger.info(f"已备份原文件到: {backup_path}")
    
    # 读取CSV
    df = pd.read_csv(csv_path)
    logger.info(f"读取CSV文件，共 {len(df)} 条记录")
    
    # 初始化URL修复器
    fixer = URLFixer()
    
    # 修复URL
    fixed_count = 0
    for idx, row in df.iterrows():
        data_sources = row['data_sources']
        if pd.isna(data_sources) or not data_sources:
            continue
        
        original_sources = str(data_sources)
        
        # 处理多个URL（用换行或分号分隔）
        if '\n' in original_sources:
            urls = [url.strip() for url in original_sources.split('\n') if url.strip()]
            fixed_urls = [fixer.fix_url_format(url) for url in urls]
            fixed_sources = '\n'.join(fixed_urls)
        elif ';' in original_sources:
            urls = [url.strip() for url in original_sources.split(';') if url.strip()]
            fixed_urls = [fixer.fix_url_format(url) for url in urls]
            fixed_sources = ';'.join(fixed_urls)
        else:
            fixed_sources = fixer.fix_url_format(original_sources)
        
        # 更新DataFrame
        if fixed_sources != original_sources:
            df.loc[idx, 'data_sources'] = fixed_sources
            fixed_count += 1
            logger.info(f"修复记录 {row['id']}: URL已更新")
    
    # 保存修复后的文件
    df.to_csv(output_path, index=False, encoding='utf-8-sig')
    logger.info(f"URL修复完成，共修复 {fixed_count} 条记录")
    logger.info(f"修复后的文件保存到: {output_path}")
    
    return output_path

class URLTimeExtractor:
    """URL时间提取器"""
    
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59'
        ]
        
        # 日期匹配模式
        self.date_patterns = [
            r'\d{4}-\d{2}-\d{2}',
            r'\d{4}/\d{2}/\d{2}',
            r'\d{2}/\d{2}/\d{4}',
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'(\d{4})-(\d{1,2})-(\d{1,2})',
            r'(\d{1,2})/(\d{1,2})/(\d{4})',
        ]
    
    def get_session(self):
        """创建请求会话"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        return session
    
    def extract_time_from_url(self, url: str) -> Optional[str]:
        """从URL路径提取时间"""
        if not url:
            return None
        
        # 从URL路径中查找日期模式
        for pattern in self.date_patterns:
            matches = re.findall(pattern, url)
            if matches:
                return matches[0] if isinstance(matches[0], str) else '-'.join(matches[0])
        
        return None
    
    def extract_time_from_meta(self, soup: BeautifulSoup) -> Optional[str]:
        """从meta标签提取时间"""
        meta_selectors = [
            'meta[property="article:published_time"]',
            'meta[name="publishdate"]',
            'meta[name="date"]',
            'meta[property="og:published_time"]',
            'meta[name="DC.date.issued"]',
            'meta[name="publication-date"]'
        ]
        
        for selector in meta_selectors:
            meta_tag = soup.select_one(selector)
            if meta_tag and meta_tag.get('content'):
                return meta_tag.get('content')
        
        return None
    
    def extract_time_from_jsonld(self, soup: BeautifulSoup) -> Optional[str]:
        """从JSON-LD结构化数据提取时间"""
        scripts = soup.find_all('script', type='application/ld+json')
        for script in scripts:
            try:
                import json
                data = json.loads(script.string)
                if isinstance(data, dict):
                    if 'datePublished' in data:
                        return data['datePublished']
                    elif 'publishedDate' in data:
                        return data['publishedDate']
                elif isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict) and 'datePublished' in item:
                            return item['datePublished']
            except:
                continue
        
        return None

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "fix":
        # 只修复URL格式
        fix_urls_in_csv()
    else:
        # 完整流程：先修复URL，再提取时间
        logger.info("开始完整处理流程...")
        
        # 步骤1：修复URL格式
        logger.info("步骤1：修复URL格式...")
        fixed_csv_path = fix_urls_in_csv()
        
        logger.info("URL修复完成！请检查修复后的文件，然后运行时间提取。")
