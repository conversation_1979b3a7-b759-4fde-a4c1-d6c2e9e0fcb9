#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
南海CSV URL修复和时间重新提取脚本
修复URL格式问题并重新尝试提取发布时间
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import re
import time
import random
from datetime import datetime
import json
from urllib.parse import urljoin, urlparse
import logging
from typing import List, Optional, Tuple
import os

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nanhai_url_fix.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class URLTimeExtractor:
    def __init__(self):
        # 多种用户代理轮换
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]

        # 时间提取的正则表达式模式
        self.date_patterns = [
            # 标准格式
            r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})',
            r'(\d{1,2}[-/]\d{1,2}[-/]\d{4})',
            # 带时间的格式
            r'(\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{2}(?::\d{2})?)',
            # URL中的日期格式
            r'/(\d{4})/(\d{2})/(\d{2})/',
            r'/(\d{4})(\d{2})(\d{2})/',
            # 文件名中的日期
            r'(\d{8})',
            r'(\d{4})(\d{2})(\d{2})',
        ]

        # 月份映射
        self.month_mapping = {
            'january': '01', 'february': '02', 'march': '03', 'april': '04',
            'may': '05', 'june': '06', 'july': '07', 'august': '08',
            'september': '09', 'october': '10', 'november': '11', 'december': '12',
            'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04',
            'jun': '06', 'jul': '07', 'aug': '08', 'sep': '09',
            'oct': '10', 'nov': '11', 'dec': '12'
        }

    def fix_url_format(self, url: str) -> str:
        """修复URL格式，将'-'替换为'/'"""
        if not url or url == '无法获取':
            return url

        # 检查是否是被错误替换的URL
        if '-' in url and ('http' in url or 'www.' in url):
            # 保护协议部分的://
            if '://' in url:
                protocol, rest = url.split('://', 1)
                # 只在域名后的路径部分替换'-'为'/'
                if '/' in rest:
                    domain_part, path_part = rest.split('/', 1)
                    # 修复路径部分
                    fixed_path = path_part.replace('-', '/')
                    fixed_url = f"{protocol}://{domain_part}/{fixed_path}"
                else:
                    # 如果没有路径，可能整个都需要修复
                    fixed_url = f"{protocol}://{rest.replace('-', '/')}"
            else:
                # 没有协议的情况
                fixed_url = url.replace('-', '/')

            logger.info(f"URL修复: {url} -> {fixed_url}")
            return fixed_url

        return url

    def get_session(self) -> requests.Session:
        """创建带有随机用户代理的会话"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        return session

    def extract_time_from_meta(self, soup: BeautifulSoup) -> Optional[str]:
        """从HTML meta标签提取时间"""
        meta_selectors = [
            'meta[property="article:published_time"]',
            'meta[name="publishdate"]',
            'meta[name="date"]',
            'meta[name="publication-date"]',
            'meta[property="og:published_time"]',
            'meta[name="DC.date.issued"]',
            'meta[name="datePublished"]',
        ]

        for selector in meta_selectors:
            meta = soup.select_one(selector)
            if meta and meta.get('content'):
                return meta.get('content')

        return None

    def extract_time_from_jsonld(self, soup: BeautifulSoup) -> Optional[str]:
        """从JSON-LD结构化数据提取时间"""
        scripts = soup.find_all('script', type='application/ld+json')
        for script in scripts:
            try:
                data = json.loads(script.string)
                if isinstance(data, dict):
                    # 查找发布时间字段
                    for key in ['datePublished', 'publishedDate', 'dateCreated']:
                        if key in data:
                            return data[key]
                elif isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            for key in ['datePublished', 'publishedDate', 'dateCreated']:
                                if key in item:
                                    return item[key]
            except (json.JSONDecodeError, AttributeError):
                continue

        return None

    def extract_time_from_url(self, url: str) -> Optional[str]:
        """从URL路径提取时间"""
        for pattern in self.date_patterns:
            matches = re.findall(pattern, url)
            if matches:
                match = matches[0]
                if isinstance(match, tuple):
                    if len(match) == 3:  # YYYY, MM, DD
                        return f"{match[0]}-{match[1].zfill(2)}-{match[2].zfill(2)} 00:00:00"
                elif len(match) == 8:  # YYYYMMDD
                    return f"{match[:4]}-{match[4:6]}-{match[6:8]} 00:00:00"

        return None

    def extract_time_from_content(self, soup: BeautifulSoup) -> Optional[str]:
        """从页面内容提取时间"""
        # 查找常见的时间容器
        time_selectors = [
            'time[datetime]',
            '.date', '.publish-date', '.publication-date',
            '.article-date', '.post-date', '.news-date',
            '[class*="date"]', '[id*="date"]'
        ]

        for selector in time_selectors:
            elements = soup.select(selector)
            for element in elements:
                # 检查datetime属性
                if element.get('datetime'):
                    return element.get('datetime')

                # 检查文本内容
                text = element.get_text().strip()
                if text:
                    for pattern in self.date_patterns:
                        matches = re.findall(pattern, text)
                        if matches:
                            return matches[0] if isinstance(matches[0], str) else '-'.join(matches[0])

        return None

    def normalize_datetime(self, date_str: str) -> str:
        """标准化日期时间格式"""
        if not date_str:
            return "无法获取"

        try:
            # 尝试解析各种格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%Y/%m/%d',
                '%m/%d/%Y',
                '%d/%m/%Y',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S.%fZ',
            ]

            for fmt in formats:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.strftime('%Y-%m-%d %H:%M:%S')
                except ValueError:
                    continue

            # 如果都失败了，尝试提取数字
            numbers = re.findall(r'\d+', date_str)
            if len(numbers) >= 3:
                year, month, day = numbers[0], numbers[1], numbers[2]
                if len(year) == 4 and 1 <= int(month) <= 12 and 1 <= int(day) <= 31:
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)} 00:00:00"

        except Exception as e:
            logger.warning(f"日期标准化失败: {date_str}, 错误: {e}")

        return "无法获取"

    def extract_publish_time(self, url: str) -> str:
        """从URL提取发布时间的主函数"""
        if not url or url == '无法获取':
            return "无法获取"

        # 修复URL格式
        fixed_url = self.fix_url_format(url)

        # 首先尝试从URL提取
        url_time = self.extract_time_from_url(fixed_url)
        if url_time and url_time != "无法获取":
            return self.normalize_datetime(url_time)

        # 尝试访问网页
        session = self.get_session()
        max_retries = 3

        for attempt in range(max_retries):
            try:
                logger.info(f"尝试访问 ({attempt + 1}/{max_retries}): {fixed_url}")

                response = session.get(
                    fixed_url,
                    timeout=15,
                    allow_redirects=True,
                    verify=False  # 忽略SSL证书问题
                )

                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')

                    # 按优先级尝试不同的提取方法
                    methods = [
                        self.extract_time_from_meta,
                        self.extract_time_from_jsonld,
                        self.extract_time_from_content,
                    ]

                    for method in methods:
                        try:
                            result = method(soup)
                            if result:
                                normalized = self.normalize_datetime(result)
                                if normalized != "无法获取":
                                    logger.info(f"成功提取时间: {fixed_url} -> {normalized}")
                                    return normalized
                        except Exception as e:
                            logger.warning(f"提取方法失败: {method.__name__}, 错误: {e}")
                            continue

                else:
                    logger.warning(f"HTTP错误 {response.status_code}: {fixed_url}")

            except requests.exceptions.RequestException as e:
                logger.warning(f"请求失败 ({attempt + 1}/{max_retries}): {fixed_url}, 错误: {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(2, 5))  # 随机延迟
                continue
            except Exception as e:
                logger.error(f"未知错误: {fixed_url}, 错误: {e}")
                break

        logger.warning(f"无法提取时间: {fixed_url}")
        return "无法获取"

def process_nanhai_csv():
    """处理南海CSV文件"""
    csv_path = 'extracted_data/南海_extracted.csv'
    backup_path = 'extracted_data/南海_extracted_backup.csv'

    # 备份原文件
    if os.path.exists(csv_path):
        import shutil
        shutil.copy2(csv_path, backup_path)
        logger.info(f"已备份原文件到: {backup_path}")

    # 读取CSV
    df = pd.read_csv(csv_path)
    logger.info(f"读取CSV文件，共 {len(df)} 条记录")

    # 筛选需要重新处理的记录
    failed_records = df[df['publish_time'] == '无法获取'].copy()
    logger.info(f"找到 {len(failed_records)} 条失败记录需要重新处理")

    if len(failed_records) == 0:
        logger.info("没有需要重新处理的记录")
        return

    # 初始化提取器
    extractor = URLTimeExtractor()

    # 统计信息
    success_count = 0
    total_count = len(failed_records)

    # 处理每条失败记录
    for idx, row in failed_records.iterrows():
        logger.info(f"处理进度: {success_count + 1}/{total_count}")

        data_sources = row['data_sources']
        if pd.isna(data_sources) or not data_sources:
            continue

        # 处理多个URL（用换行或分号分隔）
        urls = []
        if '\n' in str(data_sources):
            urls = [url.strip() for url in str(data_sources).split('\n') if url.strip()]
        elif ';' in str(data_sources):
            urls = [url.strip() for url in str(data_sources).split(';') if url.strip()]
        else:
            urls = [str(data_sources).strip()]

        # 尝试从每个URL提取时间
        extracted_time = "无法获取"
        for url in urls:
            if url and url != '无法获取':
                result = extractor.extract_publish_time(url)
                if result != "无法获取":
                    extracted_time = result
                    break

                # 添加延迟避免过于频繁的请求
                time.sleep(random.uniform(1, 3))

        # 更新DataFrame
        df.loc[idx, 'publish_time'] = extracted_time

        if extracted_time != "无法获取":
            success_count += 1
            logger.info(f"✓ 成功提取: {row['id']} -> {extracted_time}")
        else:
            logger.info(f"✗ 仍然失败: {row['id']}")

        # 每处理10条记录保存一次
        if (success_count + 1) % 10 == 0:
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            logger.info(f"中间保存完成，已处理 {success_count + 1} 条记录")

    # 最终保存
    df.to_csv(csv_path, index=False, encoding='utf-8-sig')

    # 生成报告
    final_failed = len(df[df['publish_time'] == '无法获取'])
    logger.info(f"""
    处理完成报告:
    - 原始失败记录: {total_count}
    - 成功修复记录: {success_count}
    - 仍然失败记录: {final_failed}
    - 成功率: {success_count/total_count*100:.1f}%
    """)

if __name__ == "__main__":
    process_nanhai_csv()