#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
南海CSV URL修复和时间重新提取脚本
修复URL格式问题并重新尝试提取发布时间
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import re
import time
import random
from datetime import datetime
import json
from urllib.parse import urljoin, urlparse
import logging
from typing import List, Optional, Tuple
import os
import concurrent.futures
from threading import Lock
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import concurrent.futures
from threading import Lock
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nanhai_url_fix.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class URLTimeExtractor:
    def __init__(self):
        # 多种用户代理轮换
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]

        # 时间提取的正则表达式模式
        self.date_patterns = [
            # 标准格式
            r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})',
            r'(\d{1,2}[-/]\d{1,2}[-/]\d{4})',
            # 带时间的格式
            r'(\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{2}(?::\d{2})?)',
            # URL中的日期格式
            r'/(\d{4})/(\d{2})/(\d{2})/',
            r'/(\d{4})(\d{2})(\d{2})/',
            # 文件名中的日期
            r'(\d{8})',
            r'(\d{4})(\d{2})(\d{2})',
        ]

        # 月份映射
        self.month_mapping = {
            'january': '01', 'february': '02', 'march': '03', 'april': '04',
            'may': '05', 'june': '06', 'july': '07', 'august': '08',
            'september': '09', 'october': '10', 'november': '11', 'december': '12',
            'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04',
            'jun': '06', 'jul': '07', 'aug': '08', 'sep': '09',
            'oct': '10', 'nov': '11', 'dec': '12'
        }

    def fix_url_format(self, url: str) -> str:
        """修复URL格式，智能处理被错误替换的URL"""
        if not url or url == '无法获取':
            return url

        original_url = url

        # 检查是否是被错误替换的URL（包含'-'且看起来像URL）
        if '-' in url and ('http' in url or 'www.' in url):

            # 特殊处理：如果URL看起来是被完全替换了'/'为'-'
            # 例如：https://www.pacom.mil-Media-News-News-Article-View-Article-3394632-cope-thunder-23-1-comes-to-a-close-

            if '://' in url:
                protocol, rest = url.split('://', 1)

                # 分离域名和路径
                # 域名通常不会有很多'-'，路径部分才会有
                parts = rest.split('-')

                if len(parts) > 1:
                    # 假设第一部分是域名
                    domain = parts[0]

                    # 检查域名是否合理（包含.）
                    if '.' in domain:
                        # 重建路径：将剩余部分用'/'连接
                        path_parts = parts[1:]
                        if path_parts:
                            # 过滤掉空字符串和末尾的'-'
                            path_parts = [part for part in path_parts if part]
                            if path_parts:
                                fixed_url = f"{protocol}://{domain}/" + '/'.join(path_parts)
                            else:
                                fixed_url = f"{protocol}://{domain}/"
                        else:
                            fixed_url = f"{protocol}://{domain}/"
                    else:
                        # 域名不合理，可能需要其他处理方式
                        fixed_url = url.replace('-', '/')
                else:
                    fixed_url = url
            else:
                # 没有协议的情况
                fixed_url = url.replace('-', '/')

            # 清理多余的斜杠，但保护协议部分
            if '://' in fixed_url:
                protocol_part, rest_part = fixed_url.split('://', 1)
                rest_part = re.sub(r'/+', '/', rest_part)
                fixed_url = f"{protocol_part}://{rest_part}"

            if fixed_url != original_url:
                logger.debug(f"URL修复: {original_url} -> {fixed_url}")
            return fixed_url

        return url

    def get_session(self) -> requests.Session:
        """创建带有随机用户代理的会话"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        return session

    def extract_time_from_meta(self, soup: BeautifulSoup) -> Optional[str]:
        """从HTML meta标签提取时间"""
        meta_selectors = [
            'meta[property="article:published_time"]',
            'meta[name="publishdate"]',
            'meta[name="date"]',
            'meta[name="publication-date"]',
            'meta[property="og:published_time"]',
            'meta[name="DC.date.issued"]',
            'meta[name="datePublished"]',
        ]

        for selector in meta_selectors:
            meta = soup.select_one(selector)
            if meta and meta.get('content'):
                return meta.get('content')

        return None

    def extract_time_from_jsonld(self, soup: BeautifulSoup) -> Optional[str]:
        """从JSON-LD结构化数据提取时间"""
        scripts = soup.find_all('script', type='application/ld+json')
        for script in scripts:
            try:
                data = json.loads(script.string)
                if isinstance(data, dict):
                    # 查找发布时间字段
                    for key in ['datePublished', 'publishedDate', 'dateCreated']:
                        if key in data:
                            return data[key]
                elif isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            for key in ['datePublished', 'publishedDate', 'dateCreated']:
                                if key in item:
                                    return item[key]
            except (json.JSONDecodeError, AttributeError):
                continue

        return None

    def extract_time_from_url(self, url: str) -> Optional[str]:
        """从URL路径提取时间"""
        for pattern in self.date_patterns:
            matches = re.findall(pattern, url)
            if matches:
                match = matches[0]
                if isinstance(match, tuple):
                    if len(match) == 3:  # YYYY, MM, DD
                        return f"{match[0]}-{match[1].zfill(2)}-{match[2].zfill(2)} 00:00:00"
                elif len(match) == 8:  # YYYYMMDD
                    return f"{match[:4]}-{match[4:6]}-{match[6:8]} 00:00:00"

        return None

    def extract_time_from_content(self, soup: BeautifulSoup) -> Optional[str]:
        """从页面内容提取时间"""
        # 查找常见的时间容器
        time_selectors = [
            'time[datetime]',
            '.date', '.publish-date', '.publication-date',
            '.article-date', '.post-date', '.news-date',
            '[class*="date"]', '[id*="date"]'
        ]

        for selector in time_selectors:
            elements = soup.select(selector)
            for element in elements:
                # 检查datetime属性
                if element.get('datetime'):
                    return element.get('datetime')

                # 检查文本内容
                text = element.get_text().strip()
                if text:
                    for pattern in self.date_patterns:
                        matches = re.findall(pattern, text)
                        if matches:
                            return matches[0] if isinstance(matches[0], str) else '-'.join(matches[0])

        return None

    def normalize_datetime(self, date_str: str) -> str:
        """标准化日期时间格式"""
        if not date_str:
            return "无法获取"

        try:
            # 尝试解析各种格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%Y/%m/%d',
                '%m/%d/%Y',
                '%d/%m/%Y',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S.%fZ',
            ]

            for fmt in formats:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.strftime('%Y-%m-%d %H:%M:%S')
                except ValueError:
                    continue

            # 如果都失败了，尝试提取数字
            numbers = re.findall(r'\d+', date_str)
            if len(numbers) >= 3:
                year, month, day = numbers[0], numbers[1], numbers[2]
                if len(year) == 4 and 1 <= int(month) <= 12 and 1 <= int(day) <= 31:
                    return f"{year}-{month.zfill(2)}-{day.zfill(2)} 00:00:00"

        except Exception as e:
            logger.warning(f"日期标准化失败: {date_str}, 错误: {e}")

        return "无法获取"

    def extract_publish_time(self, url: str) -> str:
        """从URL提取发布时间的主函数"""
        if not url or url == '无法获取':
            return "无法获取"

        # 修复URL格式
        fixed_url = self.fix_url_format(url)

        # 首先尝试从URL提取
        url_time = self.extract_time_from_url(fixed_url)
        if url_time and url_time != "无法获取":
            return self.normalize_datetime(url_time)

        # 尝试访问网页
        session = self.get_session()
        max_retries = 2  # 减少重试次数

        for attempt in range(max_retries):
            try:
                # 减少日志输出频率
                if attempt == 0:
                    logger.debug(f"访问: {fixed_url}")

                response = session.get(
                    fixed_url,
                    timeout=10,  # 减少超时时间
                    allow_redirects=True,
                    verify=False  # 忽略SSL证书问题
                )

                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')

                    # 按优先级尝试不同的提取方法
                    methods = [
                        self.extract_time_from_meta,
                        self.extract_time_from_jsonld,
                        self.extract_time_from_content,
                    ]

                    for method in methods:
                        try:
                            result = method(soup)
                            if result:
                                normalized = self.normalize_datetime(result)
                                if normalized != "无法获取":
                                    logger.debug(f"成功提取时间: {fixed_url} -> {normalized}")
                                    return normalized
                        except Exception as e:
                            logger.debug(f"提取方法失败: {method.__name__}, 错误: {e}")
                            continue

                else:
                    logger.debug(f"HTTP错误 {response.status_code}: {fixed_url}")

            except requests.exceptions.RequestException as e:
                logger.debug(f"请求失败 ({attempt + 1}/{max_retries}): {fixed_url}, 错误: {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(1, 2))  # 减少延迟时间
                continue
            except Exception as e:
                logger.debug(f"未知错误: {fixed_url}, 错误: {e}")
                break

        logger.warning(f"无法提取时间: {fixed_url}")
        return "无法获取"

# 全局锁和计数器
progress_lock = Lock()
success_count = 0
processed_count = 0

def process_single_record(args):
    """处理单条记录的函数"""
    global success_count, processed_count

    idx, row, extractor = args

    data_sources = row['data_sources']
    if pd.isna(data_sources) or not data_sources:
        return idx, "无法获取"

    # 处理多个URL（用换行或分号分隔）
    urls = []
    if '\n' in str(data_sources):
        urls = [url.strip() for url in str(data_sources).split('\n') if url.strip()]
    elif ';' in str(data_sources):
        urls = [url.strip() for url in str(data_sources).split(';') if url.strip()]
    else:
        urls = [str(data_sources).strip()]

    # 尝试从每个URL提取时间
    extracted_time = "无法获取"
    for url in urls:
        if url and url != '无法获取':
            result = extractor.extract_publish_time(url)
            if result != "无法获取":
                extracted_time = result
                break

            # 减少延迟，因为并发处理
            time.sleep(random.uniform(0.5, 1.5))

    # 更新进度
    with progress_lock:
        global processed_count, success_count
        processed_count += 1
        if extracted_time != "无法获取":
            success_count += 1
            logger.info(f"✓ 成功提取 ({processed_count}): {row['id']} -> {extracted_time}")
        else:
            logger.info(f"✗ 仍然失败 ({processed_count}): {row['id']}")

    return idx, extracted_time

def process_nanhai_csv():
    """处理南海CSV文件 - 并发版本"""
    global success_count, processed_count

    csv_path = 'extracted_data\南海_extracted_fixed_urls.csv'
    backup_path = 'extracted_data/南海_extracted_backup.csv'

    # 备份原文件
    if os.path.exists(csv_path):
        import shutil
        shutil.copy2(csv_path, backup_path)
        logger.info(f"已备份原文件到: {backup_path}")

    # 读取CSV
    df = pd.read_csv(csv_path)
    logger.info(f"读取CSV文件，共 {len(df)} 条记录")

    # 筛选需要重新处理的记录
    failed_records = df[df['publish_time'] == '无法获取'].copy()
    logger.info(f"找到 {len(failed_records)} 条失败记录需要重新处理")

    if len(failed_records) == 0:
        logger.info("没有需要重新处理的记录")
        return

    # 重置计数器
    success_count = 0
    processed_count = 0
    total_count = len(failed_records)

    # 准备任务参数
    tasks = []
    for idx, row in failed_records.iterrows():
        extractor = URLTimeExtractor()  # 每个线程使用独立的提取器
        tasks.append((idx, row, extractor))

    logger.info(f"开始并发处理，使用 8 个线程")

    # 使用线程池并发处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
        # 提交所有任务
        future_to_task = {executor.submit(process_single_record, task): task for task in tasks}

        # 收集结果
        results = {}
        completed = 0

        for future in concurrent.futures.as_completed(future_to_task):
            try:
                idx, extracted_time = future.result()
                results[idx] = extracted_time
                completed += 1

                # 每完成20个任务报告一次进度
                if completed % 20 == 0:
                    logger.info(f"进度: {completed}/{total_count} ({completed/total_count*100:.1f}%)")

            except Exception as e:
                task = future_to_task[future]
                logger.error(f"处理任务失败: {task[1]['id']}, 错误: {e}")

    # 更新DataFrame
    logger.info("更新CSV文件...")
    for idx, extracted_time in results.items():
        df.loc[idx, 'publish_time'] = extracted_time

    # 保存结果到新文件，避免权限问题
    output_path = 'extracted_data/南海_extracted_updated.csv'
    try:
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        logger.info(f"结果已保存到: {output_path}")
    except Exception as e:
        logger.error(f"保存文件失败: {e}")
        # 尝试保存到当前目录
        output_path = '南海_extracted_updated.csv'
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        logger.info(f"结果已保存到: {output_path}")

    # 生成报告
    final_failed = len(df[df['publish_time'] == '无法获取'])
    logger.info(f"""
    处理完成报告:
    - 原始失败记录: {total_count}
    - 成功修复记录: {success_count}
    - 仍然失败记录: {final_failed}
    - 成功率: {success_count/total_count*100:.1f}%
    """)

def test_process_sample():
    """测试处理少量记录"""
    csv_path = 'extracted_data/南海_extracted.csv'

    # 读取CSV
    df = pd.read_csv(csv_path)
    logger.info(f"读取CSV文件，共 {len(df)} 条记录")

    # 筛选需要重新处理的记录，只取前10条
    failed_records = df[df['publish_time'] == '无法获取'].head(10).copy()
    logger.info(f"测试处理前 {len(failed_records)} 条失败记录")

    if len(failed_records) == 0:
        logger.info("没有需要重新处理的记录")
        return

    # 显示将要处理的记录
    for idx, row in failed_records.iterrows():
        logger.info(f"将处理: {row['id']} - {row['data_sources'][:100]}...")

    # 询问是否继续
    response = input("是否继续处理这些记录？(y/n): ")
    if response.lower() != 'y':
        logger.info("用户取消处理")
        return

    # 处理记录
    extractor = URLTimeExtractor()
    for idx, row in failed_records.iterrows():
        logger.info(f"处理: {row['id']}")

        data_sources = row['data_sources']
        if pd.isna(data_sources) or not data_sources:
            continue

        # 处理多个URL
        urls = []
        if '\n' in str(data_sources):
            urls = [url.strip() for url in str(data_sources).split('\n') if url.strip()]
        elif ';' in str(data_sources):
            urls = [url.strip() for url in str(data_sources).split(';') if url.strip()]
        else:
            urls = [str(data_sources).strip()]

        # 显示修复后的URL
        for url in urls:
            fixed_url = extractor.fix_url_format(url)
            if fixed_url != url:
                logger.info(f"URL修复: {url} -> {fixed_url}")

        # 尝试提取时间
        extracted_time = "无法获取"
        for url in urls:
            if url and url != '无法获取':
                result = extractor.extract_publish_time(url)
                if result != "无法获取":
                    extracted_time = result
                    break

        logger.info(f"结果: {row['id']} -> {extracted_time}")
        print("-" * 80)

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_process_sample()
    else:
        process_nanhai_csv()